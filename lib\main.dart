import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';

import 'package:guest_posts_buyer/app_router.dart';
import 'package:guest_posts_buyer/firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(

    options: kIsWeb
        ? DefaultFirebaseOptions.web
        : null, // تأكد من جلب الخيارات الصحيحة للويب
  );
  runApp(MyApp());
}

class CustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.trackpad,
      };
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      scrollBehavior: CustomScrollBehavior(),
      theme: ThemeData(
          colorSchemeSeed: Colors.white,
          textTheme: TextTheme(
            displayMedium: TextStyle(fontFamily: 'Alatsi'),
            bodyMedium: TextStyle(fontFamily: 'Alatsi'),
          )),
      title: 'Dashboard',
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: false,
    );
  }
}
