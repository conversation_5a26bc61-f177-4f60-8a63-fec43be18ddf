// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:guest_posts_buyer/core/models/offer_model.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/order_details_dialog.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/user_activity_dialog.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  bool _isLoading = true;
  String? _adminUid;
  List<WebsiteModel> _websites = [];
  List<OrderModel> _orders = [];
  List<UserModel> _users = [];
  List<OfferModel> _offers = [];
  List<Map<String, dynamic>> _topPublishers = [];
  Map<String, int> _categories = {};
  StreamSubscription<QuerySnapshot>? _websitesSubscription;
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<QuerySnapshot>? _usersSubscription;
  StreamSubscription<QuerySnapshot>? _offersSubscription;

  // Pagination states
  int _publishersPage = 1;
  int _categoriesPage = 1;
  int _websitesPage = 1;
  int _ordersPage = 1;
  int _usersPage = 1;
  final int _itemsPerPage = 5;

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
  }

  Future<void> _checkAdminAccess() async {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: user.uid,
      );
      // Uncomment for production
      if (userData == null || userData['isAdmin'] != true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Access denied: Admins only'),
              backgroundColor: Colors.red,
            ),
          );
          context.go('/login');
        }
        return;
      }

      if (mounted) {
        setState(() {
          _adminUid = user.uid;
          _isLoading = true;
        });
        _subscribeToData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking admin access: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  void _subscribeToData() {
    // Subscribe to websites
    _websitesSubscription = FirebaseFirestore.instance
        .collection('websites')
        .orderBy('createdAt', descending: true)
        .limit(10)
        .snapshots()
        .listen((snapshot) {
      final websites = snapshot.docs.map((doc) {
        final data = doc.data();
        return WebsiteModel.fromMap(data..['websiteId'] = doc.id);
      }).toList();

      setState(() {
        _websites = websites;
        _updateCategories();
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading websites: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });

    // Subscribe to offers
    _offersSubscription = FirebaseFirestore.instance
        .collection('offers')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .listen((snapshot) {
      final offers = snapshot.docs.map((doc) {
        return OfferModel.fromMap(doc.data(), doc.id);
      }).toList();

      setState(() {
        _offers = offers;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading offers: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });

    // Subscribe to orders
    _ordersSubscription = FirebaseFirestore.instance
        .collection('orders')
        .orderBy('orderDate', descending: true)
        .limit(10)
        .snapshots()
        .listen((snapshot) async {
      final orders = snapshot.docs.map((doc) {
        final data = doc.data();
        return OrderModel.fromMap(data..['orderId'] = doc.id);
      }).toList();

      await _updateTopPublishers(orders);

      setState(() {
        _orders = orders;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading orders: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });

    // Subscribe to users
    _usersSubscription = FirebaseFirestore.instance
        .collection('users')
        .orderBy('createdAt', descending: true)
        .limit(10)
        .snapshots()
        .listen((snapshot) {
      final users = snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromMap(data, doc.id);
      }).toList();

      setState(() {
        _users = users;
        _isLoading = false;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading users: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _subscribeToData,
            ),
          ),
        );
      }
    });
  }

  Future<void> _updateTopPublishers(List<OrderModel> orders) async {
    // Count completed orders per publisher
    final publisherOrderCounts = <String, int>{};
    for (var order in orders.where((o) => o.status == 'Completed')) {
      if (order.publisherId != null) {
        publisherOrderCounts[order.publisherId!] =
            (publisherOrderCounts[order.publisherId!] ?? 0) + 1;
      }
    }

    // Get top publishers
    final topPublisherIds = publisherOrderCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final publisherIds = topPublisherIds.map((e) => e.key).toList();

    // Fetch publisher details
    final topPublishers = <Map<String, dynamic>>[];
    for (var publisherId in publisherIds) {
      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: publisherId,
      );
      if (userData != null) {
        final user = UserModel.fromMap(userData, publisherId);
        topPublishers.add({
          'user': user,
          'completedOrders': publisherOrderCounts[publisherId]!,
        });
      }
    }

    setState(() {
      _topPublishers = topPublishers;
    });
  }

  void _updateCategories() {
    final categories = <String, int>{};
    for (var website in _websites) {
      final websiteCategories = website.categories.isNotEmpty
          ? website.categories
          : ['Uncategorized'];
      for (var category in websiteCategories) {
        categories[category] = (categories[category] ?? 0) + 1;
      }
    }
    setState(() {
      _categories = categories;
    });
  }

  // Chart data processing methods
  List<PieChartSectionData> _getCategoriesChartData() {
    if (_categories.isEmpty) return [];

    final colors = [
      AppColors.blue,
      AppColors.green,
      AppColors.yellow,
      AppColors.red,
      AppColors.blueDark,
      AppColors.blueMedium,
      Colors.purple.shade600,
      Colors.orange.shade600,
      Colors.teal.shade600,
      Colors.pink.shade600,
    ];

    final total = _categories.values.fold(0, (sum, count) => sum + count);
    final sections = <PieChartSectionData>[];

    int colorIndex = 0;
    _categories.entries.forEach((entry) {
      final percentage = (entry.value / total * 100);
      sections.add(
        PieChartSectionData(
          color: colors[colorIndex % colors.length],
          value: entry.value.toDouble(),
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 60,
          titleStyle: const TextStyle(
            fontFamily: 'Space',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
      colorIndex++;
    });

    return sections;
  }

  List<PieChartSectionData> _getWebsiteStatusChartData() {
    if (_websites.isEmpty) return [];

    final statusCounts = <String, int>{};
    for (var website in _websites) {
      statusCounts[website.status] = (statusCounts[website.status] ?? 0) + 1;
    }

    final colors = {
      'Approved': AppColors.green,
      'Pending': AppColors.yellow,
      'Rejected': AppColors.red,
    };

    final total = statusCounts.values.fold(0, (sum, count) => sum + count);
    final sections = <PieChartSectionData>[];

    statusCounts.entries.forEach((entry) {
      final percentage = (entry.value / total * 100);
      sections.add(
        PieChartSectionData(
          color: colors[entry.key] ?? AppColors.grey,
          value: entry.value.toDouble(),
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 50,
          titleStyle: const TextStyle(
            fontFamily: 'Space',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    });

    return sections;
  }

  List<PieChartSectionData> _getOrderStatusChartData() {
    if (_orders.isEmpty) return [];

    final statusCounts = <String, int>{};
    for (var order in _orders) {
      statusCounts[order.status] = (statusCounts[order.status] ?? 0) + 1;
    }

    final colors = {
      'Completed': AppColors.green,
      'Pending': AppColors.yellow,
      'In Progress': AppColors.blue,
      'Disputed': AppColors.red,
      'Cancelled': AppColors.grey,
    };

    final total = statusCounts.values.fold(0, (sum, count) => sum + count);
    final sections = <PieChartSectionData>[];

    statusCounts.entries.forEach((entry) {
      final percentage = (entry.value / total * 100);
      sections.add(
        PieChartSectionData(
          color: colors[entry.key] ?? AppColors.grey,
          value: entry.value.toDouble(),
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 50,
          titleStyle: const TextStyle(
            fontFamily: 'Space',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    });

    return sections;
  }

  List<PieChartSectionData> _getUserRoleChartData() {
    if (_users.isEmpty) return [];

    final publisherCount = _users.where((u) => u.isPublisher).length;
    final advertiserCount = _users.length - publisherCount;

    final total = _users.length;
    final sections = <PieChartSectionData>[];

    if (publisherCount > 0) {
      final percentage = (publisherCount / total * 100);
      sections.add(
        PieChartSectionData(
          color: AppColors.green,
          value: publisherCount.toDouble(),
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 50,
          titleStyle: const TextStyle(
            fontFamily: 'Space',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (advertiserCount > 0) {
      final percentage = (advertiserCount / total * 100);
      sections.add(
        PieChartSectionData(
          color: AppColors.blue,
          value: advertiserCount.toDouble(),
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 50,
          titleStyle: const TextStyle(
            fontFamily: 'Space',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return sections;
  }

  List<BarChartGroupData> _getPublishersBarChartData() {
    if (_topPublishers.isEmpty) return [];

    final data = <BarChartGroupData>[];
    for (int i = 0; i < _topPublishers.length && i < 5; i++) {
      final publisher = _topPublishers[i];
      final completedOrders = publisher['completedOrders'] as int;

      data.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: completedOrders.toDouble(),
              color: AppColors.blue,
              width: 20,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      );
    }

    return data;
  }

  List<FlSpot> _getOrdersTimelineData() {
    if (_orders.isEmpty) return [];

    // Group orders by day for the last 30 days
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));

    final dailyCounts = <DateTime, int>{};

    for (var order in _orders) {
      final orderDate = order.orderDate?.toDate();
      if (orderDate != null && orderDate.isAfter(thirtyDaysAgo)) {
        final dayKey = DateTime(orderDate.year, orderDate.month, orderDate.day);
        dailyCounts[dayKey] = (dailyCounts[dayKey] ?? 0) + 1;
      }
    }

    final spots = <FlSpot>[];
    for (int i = 0; i < 30; i++) {
      final date = thirtyDaysAgo.add(Duration(days: i));
      final dayKey = DateTime(date.year, date.month, date.day);
      final count = dailyCounts[dayKey] ?? 0;
      spots.add(FlSpot(i.toDouble(), count.toDouble()));
    }

    return spots;
  }

  void _viewWebsiteDetails(WebsiteModel website) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(website.url),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Status: ${website.status}'),
              Text(
                  'Categories: ${website.categories.isNotEmpty ? website.categories.join(", ") : "N/A"}'),
              Text('DA: ${website.da}'),
              Text('DR: ${website.dr}'),
              Text('Traffic: ${website.traffic}'),
              Text('Price: \$${website.pricing.toStringAsFixed(2)}'),
              Text('Created: ${_formatDate(website.createdAt)}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _viewOrderDetails(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => OrderDetailsDialog(
        order: order,
        website: null,
        buyerName: 'Unknown',
        buyerEmail: 'N/A',
        publisher: null,
        onChangeStatus: (status) async {
          final now = Timestamp.now();
          final statusDetail = '$status by Admin';

          // Set appropriate date fields based on status
          final Map<String, dynamic> data = {
            'status': status,
            'lastUpdated': now,
            'actionBy': _adminUid,
            'actionTimestamp': now,
            'statusDetail': statusDetail,
          };

          // Add specific date fields based on status
          if (status == 'In Progress') {
            data['inProgressDate'] = now;
          } else if (status == 'Approved') {
            data['approvalDate'] = now;
          } else if (status == 'Completed') {
            data['completionDate'] = now;
          }

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: order.orderId!,
            data: data,
          );
        },
        onAssignPublisher: (publisherId) async {
          final now = Timestamp.now();
          final action = publisherId != null
              ? 'Publisher assigned'
              : 'Publisher unassigned';
          final actionDetail = '$action by Admin';

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: order.orderId!,
            data: {
              'publisherId': publisherId,
              'lastUpdated': now,
              'actionBy': _adminUid,
              'actionTimestamp': now,
              'statusDetail': actionDetail,
            },
          );
        },
        onUpdateDispute: (isDisputed, note, disputeStatus) async {
          final now = Timestamp.now();
          final disputeAction =
              isDisputed ? 'Dispute flagged' : 'Dispute resolved';
          final disputeStatusDetail = '$disputeAction by Admin';

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: order.orderId!,
            data: {
              'isDisputed': isDisputed,
              'disputeNote': note,
              'disputeStatus': disputeStatus,
              'lastUpdated': now,
              'actionBy': _adminUid,
              'actionTimestamp': now,
              'statusDetail': disputeStatusDetail,
            },
          );
        },
        firestoreService: _firestoreService,
      ),
    );
  }

  void _viewUserActivity(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => UserActivityDialog(
        user: user,
        firestoreService: _firestoreService,
      ),
    );
  }

  @override
  void dispose() {
    _websitesSubscription?.cancel();
    _ordersSubscription?.cancel();
    _usersSubscription?.cancel();
    _offersSubscription?.cancel();
    super.dispose();
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
    required double cardWidth,
    VoidCallback? onTap,
  }) {
    final isSmallScreen = cardWidth < 200;

    // Determine which gradient to use based on the color
    LinearGradient gradient;
    if (color == AppColors.blue ||
        color == AppColors.primaryBlue ||
        color == Colors.blue.shade700) {
      gradient = AppColors.blueGradient;
    } else if (color == AppColors.green || color == Colors.green.shade600) {
      gradient = AppColors.greenGradient;
    } else if (color == AppColors.yellow || color == AppColors.yellow) {
      gradient = AppColors.yellowGradient;
    } else if (color == AppColors.red || color == Colors.red.shade600) {
      gradient = AppColors.redGradient;
    } else {
      gradient = AppColors.blueGradient;
    }

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: cardWidth,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(20),
              blurRadius: 10,
              offset: const Offset(0, 4),
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.dark,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child:
                      Icon(icon, color: color, size: isSmallScreen ? 20 : 24),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 24 : 32,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  TextStyle _headerStyle(bool isSmallScreen) {
    return TextStyle(
      fontFamily: 'Space',
      fontSize: isSmallScreen ? 13 : 15,
      fontWeight: FontWeight.w600,
      color: AppColors.blueDark,
      letterSpacing: 0.5,
    );
  }

  // Modern Chart Card Builder
  Widget _buildModernChartCard({
    required String title,
    required Widget chart,
    required bool isSmallScreen,
    List<Widget>? legends,
    double? height,
    bool showTitle = true,
    EdgeInsets? margin,
    EdgeInsets? padding,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: AppColors.blueLight.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.blue.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: AppColors.blue.withOpacity(0.04),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitle) ...[
              Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppColors.blue,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: isSmallScreen ? 18 : 20,
                        fontWeight: FontWeight.w700,
                        color: AppColors.dark,
                        letterSpacing: -0.3,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
            SizedBox(
              height: height ?? (isSmallScreen ? 240 : 300),
              child: chart,
            ),
            if (legends != null) ...[
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.blueLightest.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Wrap(
                  spacing: 16,
                  runSpacing: 12,
                  children: legends,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveChartTableLayout({
    required String sectionTitle,
    required Widget chart,
    required Widget table,
    required bool isSmallScreen,
    required bool isWideScreen,
    List<Widget>? legends,
    bool chartOnLeft = true,
    Widget? emptyState,
    bool hasData = true,
  }) {
    // Section header
    final sectionHeader = Row(
      children: [
        Container(
          height: 24,
          width: 4,
          decoration: BoxDecoration(
            color: AppColors.blue,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          sectionTitle,
          style: TextStyle(
            fontFamily: 'Space',
            fontSize: isSmallScreen ? 20 : 24,
            fontWeight: FontWeight.bold,
            color: AppColors.dark,
          ),
        ),
      ],
    );

    if (!hasData) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          sectionHeader,
          const SizedBox(height: 16),
          if (emptyState != null) emptyState,
          const SizedBox(height: 32),
        ],
      );
    }

    // Responsive layout logic
    if (isSmallScreen) {
      // Mobile: Stack vertically
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          sectionHeader,
          const SizedBox(height: 16),
          _buildModernChartCard(
            title: '',
            chart: chart,
            isSmallScreen: isSmallScreen,
            legends: legends,
            height: 200,
            showTitle: false,
          ),
          const SizedBox(height: 16),
          table,
          const SizedBox(height: 32),
        ],
      );
    } else if (isWideScreen) {
      // Desktop: Side by side with optimal proportions
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          sectionHeader,
          const SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: chartOnLeft
                ? [
                    Expanded(
                      flex: 2,
                      child: _buildModernChartCard(
                        title: '',
                        chart: chart,
                        isSmallScreen: isSmallScreen,
                        legends: legends,
                        height: 280,
                        showTitle: false,
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      flex: 3,
                      child: table,
                    ),
                  ]
                : [
                    Expanded(
                      flex: 3,
                      child: table,
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      flex: 2,
                      child: _buildModernChartCard(
                        title: '',
                        chart: chart,
                        isSmallScreen: isSmallScreen,
                        legends: legends,
                        height: 280,
                        showTitle: false,
                      ),
                    ),
                  ],
          ),
          const SizedBox(height: 32),
        ],
      );
    } else {
      // Tablet: Stack vertically with larger charts
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          sectionHeader,
          const SizedBox(height: 16),
          _buildModernChartCard(
            title: '',
            chart: chart,
            isSmallScreen: isSmallScreen,
            legends: legends,
            height: 240,
            showTitle: false,
          ),
          const SizedBox(height: 16),
          table,
          const SizedBox(height: 32),
        ],
      );
    }
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Space',
            fontSize: 12,
            color: AppColors.dark,
          ),
        ),
      ],
    );
  }

  Widget _buildOrdersTimelineChart(bool isSmallScreen) {
    final spots = _getOrdersTimelineData();
    if (spots.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timeline,
              size: 48,
              color: AppColors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No order data available',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppColors.blueLight.withAlpha(50),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 5,
              getTitlesWidget: (double value, TitleMeta meta) {
                final date =
                    DateTime.now().subtract(Duration(days: 30 - value.toInt()));
                return Text(
                  '${date.day}/${date.month}',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 10 : 12,
                    color: AppColors.grey,
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 10 : 12,
                    color: AppColors.grey,
                  ),
                );
              },
              reservedSize: 32,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: AppColors.blueLight.withAlpha(50)),
        ),
        minX: 0,
        maxX: 29,
        minY: 0,
        maxY: spots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b) + 1,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            gradient: LinearGradient(
              colors: [AppColors.blue, AppColors.blueMedium],
            ),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: AppColors.blue,
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  AppColors.blue.withAlpha(50),
                  AppColors.blue.withAlpha(10),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPieChart(
      List<PieChartSectionData> sections, bool isSmallScreen) {
    if (sections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.pie_chart,
              size: 48,
              color: AppColors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: sections,
        borderData: FlBorderData(show: false),
        sectionsSpace: 2,
        centerSpaceRadius: 0,
        startDegreeOffset: -90,
      ),
    );
  }

  Widget _buildDonutChart(
      List<PieChartSectionData> sections, bool isSmallScreen) {
    if (sections.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.donut_small,
              size: 48,
              color: AppColors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: sections,
        borderData: FlBorderData(show: false),
        sectionsSpace: 2,
        centerSpaceRadius: 80,
        startDegreeOffset: -90,
      ),
    );
  }

  Widget _buildBarChart(List<BarChartGroupData> barGroups, bool isSmallScreen) {
    if (barGroups.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: AppColors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: barGroups
                .map((group) => group.barRods.first.toY)
                .reduce((a, b) => a > b ? a : b) +
            2,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => AppColors.dark,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final publisherName = _topPublishers.length > groupIndex
                  ? (_topPublishers[groupIndex]['user'] as UserModel).name
                  : 'Publisher ${groupIndex + 1}';
              return BarTooltipItem(
                '$publisherName\n${rod.toY.round()} orders',
                const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Space',
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (double value, TitleMeta meta) {
                final index = value.toInt();
                if (index >= 0 && index < _topPublishers.length) {
                  final user = _topPublishers[index]['user'] as UserModel;
                  final name = user.name.length > 8
                      ? '${user.name.substring(0, 8)}...'
                      : user.name;
                  return Text(
                    name,
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 10 : 12,
                      color: AppColors.grey,
                    ),
                  );
                }
                return const Text('');
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 10 : 12,
                    color: AppColors.grey,
                  ),
                );
              },
              reservedSize: 32,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: AppColors.blueLight.withAlpha(50)),
        ),
        barGroups: barGroups,
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppColors.blueLight.withAlpha(50),
              strokeWidth: 1,
            );
          },
        ),
      ),
    );
  }

  DataRow _buildPublisherDataRow(
      Map<String, dynamic> publisher, bool isSmallScreen) {
    final user = publisher['user'] as UserModel;
    final completedOrders = publisher['completedOrders'] as int;

    return DataRow(
      cells: [
        DataCell(
          Text(
            user.name,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              color: AppColors.dark,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        DataCell(
          Text(
            user.email,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              color: AppColors.dark.withOpacity(0.8),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              completedOrders.toString(),
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 13 : 14,
                fontWeight: FontWeight.bold,
                color: AppColors.blue,
              ),
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(
              Icons.visibility_rounded,
              size: isSmallScreen ? 18 : 20,
              color: AppColors.blue,
            ),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.blue.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () => showDialog(
              context: context,
              builder: (context) => UserActivityDialog(
                user: user,
                firestoreService: _firestoreService,
              ),
            ),
            tooltip: 'View Activity',
          ),
        ),
      ],
    );
  }

  DataRow _buildCategoryDataRow(
      MapEntry<String, int> entry, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            entry.key,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              color: AppColors.dark,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              entry.value.toString(),
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 13 : 14,
                fontWeight: FontWeight.bold,
                color: AppColors.green,
              ),
            ),
          ),
        ),
      ],
    );
  }

  DataRow _buildWebsiteDataRow(WebsiteModel website, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            website.url.length > 30
                ? '${website.url.substring(0, 30)}...'
                : website.url,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              color: AppColors.dark,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: website.status == 'Approved'
                  ? AppColors.green.withOpacity(0.1)
                  : website.status == 'Pending'
                      ? AppColors.yellow.withOpacity(0.1)
                      : AppColors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              website.status,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 13,
                fontWeight: FontWeight.w600,
                color: website.status == 'Approved'
                    ? AppColors.green
                    : website.status == 'Pending'
                        ? Colors.orange.shade700
                        : AppColors.red,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            '\$${website.pricing.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              fontWeight: FontWeight.bold,
              color: AppColors.dark,
            ),
          ),
        ),
        DataCell(
          Text(
            DateFormat('MMM dd, yyyy').format(website.createdAt.toDate()),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              fontStyle: FontStyle.italic,
              color: AppColors.dark.withOpacity(0.8),
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(
              Icons.visibility_rounded,
              size: isSmallScreen ? 18 : 20,
              color: AppColors.blue,
            ),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.blue.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () => _viewWebsiteDetails(website),
            tooltip: 'View Details',
          ),
        ),
      ],
    );
  }

  Widget _buildModernDataTable({
    required List<DataColumn> columns,
    required List<DataRow> rows,
    required bool isSmallScreen,
    required bool isWideScreen,
    Widget? paginationControls,
    Widget? actionButton,
    String? title,
    EdgeInsets? margin,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: AppColors.blueLight.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.blue.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: AppColors.blue.withOpacity(0.04),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppColors.blue,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 18 : 20,
                      fontWeight: FontWeight.w700,
                      color: AppColors.dark,
                      letterSpacing: -0.3,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: AppColors.blueLight.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columnSpacing: isWideScreen ? 32 : (isSmallScreen ? 16 : 24),
                  headingRowHeight: 56,
                  dataRowHeight: 60,
                  headingRowColor: MaterialStateProperty.all(
                    AppColors.blueLightest.withOpacity(0.6),
                  ),
                  dataRowColor: MaterialStateProperty.resolveWith((states) {
                    if (states.contains(MaterialState.hovered)) {
                      return AppColors.blueLightest.withOpacity(0.3);
                    }
                    return Colors.transparent;
                  }),
                  dividerThickness: 1,
                  border: TableBorder(
                    horizontalInside: BorderSide(
                      color: AppColors.blueLight.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  columns: columns.map((column) {
                    return DataColumn(
                      label: DefaultTextStyle(
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: isSmallScreen ? 14 : 16,
                          fontWeight: FontWeight.w700,
                          color: AppColors.blueDark,
                          letterSpacing: 0.3,
                        ),
                        child: column.label,
                      ),
                    );
                  }).toList(),
                  rows: rows,
                ),
              ),
            ),
          ),
          if (paginationControls != null || actionButton != null) ...[
            Padding(
              padding: const EdgeInsets.all(24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (paginationControls != null)
                    paginationControls
                  else
                    const SizedBox.shrink(),
                  if (actionButton != null) actionButton,
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Modern section header builder
  Widget _buildModernSectionHeader(String title, bool isSmallScreen) {
    return Row(
      children: [
        Container(
          height: 28,
          width: 4,
          decoration: BoxDecoration(
            color: AppColors.blue,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontFamily: 'Space',
            fontSize: isSmallScreen ? 22 : 28,
            fontWeight: FontWeight.w800,
            color: AppColors.dark,
            letterSpacing: -0.5,
          ),
        ),
      ],
    );
  }

  Widget _buildModernChartTableSection({
    required String title,
    required Widget chart,
    required List<DataColumn> tableColumns,
    required List<DataRow> tableRows,
    required int currentPage,
    required int totalItems,
    required Function(int) onPageChanged,
    required VoidCallback onSeeMore,
    required bool hasData,
    required IconData emptyIcon,
    required String emptyMessage,
    required bool isSmallScreen,
    required bool isWideScreen,
    bool chartOnLeft = true,
    List<Widget>? legends,
  }) {
    if (!hasData) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernSectionHeader(title, isSmallScreen),
          const SizedBox(height: 20),
          _buildModernEmptyState(emptyIcon, emptyMessage, isSmallScreen),
          const SizedBox(height: 32),
        ],
      );
    }

    final paginationControls = _buildModernPaginationControls(
      currentPage: currentPage,
      totalItems: totalItems,
      onPageChanged: onPageChanged,
      isSmallScreen: isSmallScreen,
    );

    final actionButton = _buildModernActionButton(
      onPressed: onSeeMore,
      isSmallScreen: isSmallScreen,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModernSectionHeader(title, isSmallScreen),
        const SizedBox(height: 24),
        if (isSmallScreen)
          // Mobile: Stack vertically
          Column(
            children: [
              _buildModernChartCard(
                title: '',
                chart: chart,
                isSmallScreen: isSmallScreen,
                legends: legends,
                height: 240,
                showTitle: false,
              ),
              const SizedBox(height: 20),
              _buildModernDataTable(
                columns: tableColumns,
                rows: tableRows,
                isSmallScreen: isSmallScreen,
                isWideScreen: isWideScreen,
                paginationControls: paginationControls,
                actionButton: actionButton,
              ),
            ],
          )
        else
          // Desktop/Tablet: Side by side
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: chartOnLeft
                ? [
                    Expanded(
                      flex: 2,
                      child: _buildModernChartCard(
                        title: '',
                        chart: chart,
                        isSmallScreen: isSmallScreen,
                        legends: legends,
                        height: 320,
                        showTitle: false,
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      flex: 3,
                      child: _buildModernDataTable(
                        columns: tableColumns,
                        rows: tableRows,
                        isSmallScreen: isSmallScreen,
                        isWideScreen: isWideScreen,
                        paginationControls: paginationControls,
                        actionButton: actionButton,
                      ),
                    ),
                  ]
                : [
                    Expanded(
                      flex: 3,
                      child: _buildModernDataTable(
                        columns: tableColumns,
                        rows: tableRows,
                        isSmallScreen: isSmallScreen,
                        isWideScreen: isWideScreen,
                        paginationControls: paginationControls,
                        actionButton: actionButton,
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      flex: 2,
                      child: _buildModernChartCard(
                        title: '',
                        chart: chart,
                        isSmallScreen: isSmallScreen,
                        legends: legends,
                        height: 320,
                        showTitle: false,
                      ),
                    ),
                  ],
          ),
        const SizedBox(height: 32),
      ],
    );
  }

  // Modern helper methods
  Widget _buildModernEmptyState(
      IconData icon, String message, bool isSmallScreen) {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: AppColors.blueLight.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.blue.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.blueLightest.withOpacity(0.5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                icon,
                size: isSmallScreen ? 48 : 64,
                color: AppColors.blue.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              message,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 16 : 18,
                color: AppColors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernPaginationControls({
    required int currentPage,
    required int totalItems,
    required Function(int) onPageChanged,
    required bool isSmallScreen,
  }) {
    final totalPages = (totalItems / _itemsPerPage).ceil();
    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.blueLightest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.blueLight.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: Icon(
              Icons.chevron_left_rounded,
              size: isSmallScreen ? 20 : 24,
              color: currentPage > 1 ? AppColors.blue : AppColors.grey,
            ),
            style: IconButton.styleFrom(
              backgroundColor: currentPage > 1
                  ? AppColors.blue.withOpacity(0.1)
                  : Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed:
                currentPage > 1 ? () => onPageChanged(currentPage - 1) : null,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              '$currentPage of $totalPages',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.chevron_right_rounded,
              size: isSmallScreen ? 20 : 24,
              color: currentPage < totalPages ? AppColors.blue : AppColors.grey,
            ),
            style: IconButton.styleFrom(
              backgroundColor: currentPage < totalPages
                  ? AppColors.blue.withOpacity(0.1)
                  : Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: currentPage < totalPages
                ? () => onPageChanged(currentPage + 1)
                : null,
          ),
        ],
      ),
    );
  }

  Widget _buildModernActionButton({
    required VoidCallback onPressed,
    required bool isSmallScreen,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
        padding: EdgeInsets.symmetric(
          horizontal: isSmallScreen ? 16 : 20,
          vertical: isSmallScreen ? 12 : 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: AppColors.blue.withOpacity(0.3),
      ),
      icon: const Icon(Icons.arrow_forward_rounded, size: 18),
      label: Text(
        'View All',
        style: TextStyle(
          fontFamily: 'Space',
          fontSize: isSmallScreen ? 14 : 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isSmallScreen = screenWidth < 768;

        final isWideScreen = screenWidth >= 1200;

        if (_auth.currentUser == null || _adminUid == null) {
          return const Center(
              child: Text('Please sign in to view the dashboard'));
        }

        final crossAxisCount = screenWidth < 600
            ? 1
            : screenWidth < 900
                ? 2
                : 5;
        final cardWidth = screenWidth < 600
            ? screenWidth * 0.9
            : screenWidth / crossAxisCount - 32;

        // Paginated data
        final publishersStartIndex = (_publishersPage - 1) * _itemsPerPage;
        final publishersEndIndex = publishersStartIndex + _itemsPerPage;
        final paginatedPublishers = _topPublishers
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= publishersStartIndex &&
                entry.key < publishersEndIndex)
            .map((entry) => entry.value)
            .toList();

        final categoriesStartIndex = (_categoriesPage - 1) * _itemsPerPage;
        final categoriesEndIndex = categoriesStartIndex + _itemsPerPage;
        final paginatedCategories = _categories.entries
            .toList()
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= categoriesStartIndex &&
                entry.key < categoriesEndIndex)
            .map((entry) => entry.value)
            .toList();

        final websitesStartIndex = (_websitesPage - 1) * _itemsPerPage;
        final websitesEndIndex = websitesStartIndex + _itemsPerPage;
        final paginatedWebsites = _websites
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= websitesStartIndex && entry.key < websitesEndIndex)
            .map((entry) => entry.value)
            .toList();

        final ordersStartIndex = (_ordersPage - 1) * _itemsPerPage;
        final ordersEndIndex = ordersStartIndex + _itemsPerPage;
        final paginatedOrders = _orders
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= ordersStartIndex && entry.key < ordersEndIndex)
            .map((entry) => entry.value)
            .toList();

        final usersStartIndex = (_usersPage - 1) * _itemsPerPage;
        final usersEndIndex = usersStartIndex + _itemsPerPage;
        final paginatedUsers = _users
            .asMap()
            .entries
            .where((entry) =>
                entry.key >= usersStartIndex && entry.key < usersEndIndex)
            .map((entry) => entry.value)
            .toList();

        final completedOrders =
            _orders.where((o) => o.status == 'Completed').length;

        return Scaffold(
          backgroundColor: AppColors.backgroundColor,
          body: _isLoading
              ? Center(
                  child: CircularProgressIndicator(
                    color: AppColors.blue,
                  ),
                )
              : SingleChildScrollView(
                  padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.blue.withAlpha(15),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Admin Dashboard',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: isSmallScreen ? 24 : 32,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.dark,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Welcome back, Admin',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: isSmallScreen ? 14 : 16,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.blueLightest,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.dashboard_customize,
                                color: AppColors.blue,
                                size: isSmallScreen ? 24 : 28,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      GridView.count(
                        crossAxisCount: crossAxisCount,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(
                            vertical: 16, horizontal: 8),
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        childAspectRatio: 2,
                        children: [
                          _buildSummaryCard(
                            title: 'Total Websites',
                            value: _websites.length.toString(),
                            color: AppColors.blue,
                            icon: Icons.web,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/websites'),
                          ),
                          _buildSummaryCard(
                            title: 'Pending Websites',
                            value: _websites
                                .where((w) => w.status == 'Pending')
                                .length
                                .toString(),
                            color: AppColors.yellow,
                            icon: Icons.hourglass_empty,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/websites'),
                          ),
                          _buildSummaryCard(
                            title: 'Total Orders',
                            value: _orders.length.toString(),
                            color: AppColors.green,
                            icon: Icons.shopping_cart,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/orders'),
                          ),
                          _buildSummaryCard(
                            title: 'Completed Orders',
                            value: completedOrders.toString(),
                            color: AppColors.green,
                            icon: Icons.check_circle,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/orders'),
                          ),
                          _buildSummaryCard(
                            title: 'Disputed Orders',
                            value: _orders
                                .where((o) => o.isDisputed ?? false)
                                .length
                                .toString(),
                            color: AppColors.red,
                            icon: Icons.warning,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/orders'),
                          ),
                          _buildSummaryCard(
                            title: 'Total Users',
                            value: _users.length.toString(),
                            color: Colors.blue.shade700,
                            icon: Icons.people,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Active Users',
                            value: _users
                                .where((u) => !u.isBlocked)
                                .length
                                .toString(),
                            color: Colors.orange.shade700,
                            icon: Icons.person,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Publishers',
                            value: _users
                                .where((u) => u.isPublisher)
                                .length
                                .toString(),
                            color: Colors.green.shade600,
                            icon: Icons.publish,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Blocked Users',
                            value: _users
                                .where((u) => u.isBlocked)
                                .length
                                .toString(),
                            color: Colors.red.shade600,
                            icon: Icons.block,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/users'),
                          ),
                          _buildSummaryCard(
                            title: 'Manage Offers',
                            value: _offers
                                .where((o) => o.isActive)
                                .length
                                .toString(),
                            color: Colors.purple.shade600,
                            icon: Icons.local_offer,
                            cardWidth: cardWidth,
                            onTap: () => context.go('/offers'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),

                      // Orders Timeline Chart - Full Width
                      _buildModernSectionHeader(
                          'Orders Timeline (Last 30 Days)', isSmallScreen),
                      const SizedBox(height: 20),
                      _buildModernChartCard(
                        title: '',
                        chart: _buildOrdersTimelineChart(isSmallScreen),
                        isSmallScreen: isSmallScreen,
                        showTitle: false,
                        height: isSmallScreen ? 280 : 350,
                      ),
                      const SizedBox(height: 32),

                      // Top Publishers Section
                      _buildModernChartTableSection(
                        title: 'Top Publishers Performance',
                        chart: _buildBarChart(
                            _getPublishersBarChartData(), isSmallScreen),
                        tableColumns: [
                          DataColumn(
                              label: Text('Name',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Email',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Orders',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Actions',
                                  style: _headerStyle(isSmallScreen))),
                        ],
                        tableRows: paginatedPublishers
                            .map((publisher) => _buildPublisherDataRow(
                                publisher, isSmallScreen))
                            .toList(),
                        currentPage: _publishersPage,
                        totalItems: _topPublishers.length,
                        onPageChanged: (page) =>
                            setState(() => _publishersPage = page),
                        onSeeMore: () => context.go('/users'),
                        hasData: _topPublishers.isNotEmpty,
                        emptyIcon: Icons.publish,
                        emptyMessage: 'No publishers found',
                        isSmallScreen: isSmallScreen,
                        isWideScreen: isWideScreen,
                        chartOnLeft: true,
                      ),

                      // Website Categories Section
                      _buildModernChartTableSection(
                        title: 'Website Categories Distribution',
                        chart: _buildPieChart(
                            _getCategoriesChartData(), isSmallScreen),
                        tableColumns: [
                          DataColumn(
                              label: Text('Category',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Websites',
                                  style: _headerStyle(isSmallScreen))),
                        ],
                        tableRows: paginatedCategories
                            .map((entry) =>
                                _buildCategoryDataRow(entry, isSmallScreen))
                            .toList(),
                        currentPage: _categoriesPage,
                        totalItems: _categories.length,
                        onPageChanged: (page) =>
                            setState(() => _categoriesPage = page),
                        onSeeMore: () => context.go('/websites'),
                        hasData: _categories.isNotEmpty,
                        emptyIcon: Icons.category,
                        emptyMessage: 'No categories found',
                        isSmallScreen: isSmallScreen,
                        isWideScreen: isWideScreen,
                        chartOnLeft: false,
                        legends: _categories.entries.take(5).map((entry) {
                          final colors = [
                            AppColors.blue,
                            AppColors.green,
                            AppColors.yellow,
                            AppColors.red,
                            AppColors.blueDark,
                          ];
                          final colorIndex =
                              _categories.keys.toList().indexOf(entry.key);
                          return _buildLegendItem(
                            '${entry.key} (${entry.value})',
                            colors[colorIndex % colors.length],
                          );
                        }).toList(),
                      ),
                      // Recent Websites Section
                      _buildModernChartTableSection(
                        title: 'Recent Websites Status',
                        chart: _buildDonutChart(
                            _getWebsiteStatusChartData(), isSmallScreen),
                        tableColumns: [
                          DataColumn(
                              label: Text('URL',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Status',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Price',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Created',
                                  style: _headerStyle(isSmallScreen))),
                          DataColumn(
                              label: Text('Actions',
                                  style: _headerStyle(isSmallScreen))),
                        ],
                        tableRows: paginatedWebsites
                            .map((website) =>
                                _buildWebsiteDataRow(website, isSmallScreen))
                            .toList(),
                        currentPage: _websitesPage,
                        totalItems: _websites.length,
                        onPageChanged: (page) =>
                            setState(() => _websitesPage = page),
                        onSeeMore: () => context.go('/websites'),
                        hasData: _websites.isNotEmpty,
                        emptyIcon: Icons.web,
                        emptyMessage: 'No websites found',
                        isSmallScreen: isSmallScreen,
                        isWideScreen: isWideScreen,
                        chartOnLeft: true,
                        legends: [
                          _buildLegendItem('Approved', AppColors.green),
                          _buildLegendItem('Pending', AppColors.yellow),
                          _buildLegendItem('Rejected', AppColors.red),
                        ],
                      ),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Container(
                            height: 24,
                            width: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blue,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Recent Orders',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.dark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Order Status Chart
                      if (_orders.isNotEmpty)
                        _buildModernChartCard(
                          title: 'Order Status Distribution',
                          chart: _buildPieChart(
                              _getOrderStatusChartData(), isSmallScreen),
                          isSmallScreen: isSmallScreen,
                          legends: [
                            _buildLegendItem('Completed', AppColors.green),
                            _buildLegendItem('Pending', AppColors.yellow),
                            _buildLegendItem('In Progress', AppColors.blue),
                            _buildLegendItem('Disputed', AppColors.red),
                          ],
                        ),

                      paginatedOrders.isEmpty
                          ? _buildModernEmptyState(
                              Icons.shopping_cart,
                              'No orders found',
                              isSmallScreen,
                            )
                          : _buildModernDataTable(
                              columns: [
                                DataColumn(
                                  label: Text('Order ID',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Status',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Price',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Ordered',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Actions',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                              ],
                              rows: paginatedOrders
                                  .map((order) =>
                                      _buildOrderDataRow(order, isSmallScreen))
                                  .toList(),
                              isSmallScreen: isSmallScreen,
                              isWideScreen: isWideScreen,
                              paginationControls:
                                  _buildModernPaginationControls(
                                currentPage: _ordersPage,
                                totalItems: _orders.length,
                                onPageChanged: (page) =>
                                    setState(() => _ordersPage = page),
                                isSmallScreen: isSmallScreen,
                              ),
                              actionButton: _buildModernActionButton(
                                onPressed: () => context.go('/orders'),
                                isSmallScreen: isSmallScreen,
                              ),
                            ),
                      const SizedBox(height: 32),
                      Row(
                        children: [
                          Container(
                            height: 24,
                            width: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blue,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Recent Users',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.dark,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // User Role Chart
                      if (_users.isNotEmpty)
                        _buildModernChartCard(
                          title: 'User Role Distribution',
                          chart: _buildDonutChart(
                              _getUserRoleChartData(), isSmallScreen),
                          isSmallScreen: isSmallScreen,
                          legends: [
                            _buildLegendItem('Publishers', AppColors.green),
                            _buildLegendItem('Advertisers', AppColors.blue),
                          ],
                        ),

                      paginatedUsers.isEmpty
                          ? _buildModernEmptyState(
                              Icons.person,
                              'No users found',
                              isSmallScreen,
                            )
                          : _buildModernDataTable(
                              columns: [
                                DataColumn(
                                  label: Text('Name',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Email',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Role',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Created',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                                DataColumn(
                                  label: Text('Actions',
                                      style: _headerStyle(isSmallScreen)),
                                ),
                              ],
                              rows: paginatedUsers
                                  .map((user) =>
                                      _buildUserDataRow(user, isSmallScreen))
                                  .toList(),
                              isSmallScreen: isSmallScreen,
                              isWideScreen: isWideScreen,
                              paginationControls:
                                  _buildModernPaginationControls(
                                currentPage: _usersPage,
                                totalItems: _users.length,
                                onPageChanged: (page) =>
                                    setState(() => _usersPage = page),
                                isSmallScreen: isSmallScreen,
                              ),
                              actionButton: _buildModernActionButton(
                                onPressed: () => context.go('/users'),
                                isSmallScreen: isSmallScreen,
                              ),
                            ),
                    ],
                  ),
                ),
        );
      },
    );
  }

  DataRow _buildOrderDataRow(OrderModel order, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            order.orderId ?? 'N/A',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              color: AppColors.blue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: order.status == 'Completed'
                  ? AppColors.green.withOpacity(0.1)
                  : order.status == 'In Progress' || order.status == 'Pending'
                      ? AppColors.yellow.withOpacity(0.1)
                      : AppColors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              order.status,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 13,
                fontWeight: FontWeight.w600,
                color: order.status == 'Completed'
                    ? AppColors.green
                    : order.status == 'In Progress' || order.status == 'Pending'
                        ? Colors.orange.shade700
                        : AppColors.red,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            '\$${order.totalPrice.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              fontWeight: FontWeight.bold,
              color: AppColors.dark,
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(order.orderDate),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              fontStyle: FontStyle.italic,
              color: AppColors.dark.withOpacity(0.8),
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(
              Icons.visibility_rounded,
              color: AppColors.blue,
              size: isSmallScreen ? 18 : 20,
            ),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.blue.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () => _viewOrderDetails(order),
            tooltip: 'View Details',
          ),
        ),
      ],
    );
  }

  DataRow _buildUserDataRow(UserModel user, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            user.name,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              color: AppColors.dark,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        DataCell(
          Text(
            user.email,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              color: AppColors.dark.withOpacity(0.8),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: user.isPublisher
                  ? AppColors.green.withOpacity(0.1)
                  : AppColors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              user.isPublisher ? 'Publisher' : 'Advertiser',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 13,
                fontWeight: FontWeight.w600,
                color: user.isPublisher ? AppColors.green : AppColors.blue,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(user.createdAt),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 14,
              fontStyle: FontStyle.italic,
              color: AppColors.dark.withOpacity(0.8),
            ),
          ),
        ),
        DataCell(
          IconButton(
            icon: Icon(
              Icons.visibility_rounded,
              color: AppColors.blue,
              size: isSmallScreen ? 18 : 20,
            ),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.blue.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () => _viewUserActivity(user),
            tooltip: 'View Activity',
          ),
        ),
      ],
    );
  }
}
