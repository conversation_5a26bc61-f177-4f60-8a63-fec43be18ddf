name: guest_posts_buyer
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  go_router: ^14.8.1
  firebase_core: ^3.12.1
  firebase_auth: ^5.5.1
  cloud_firestore: ^5.6.5
  google_sign_in: ^6.2.2
  easy_sidemenu: ^0.6.1
  font_awesome_flutter: ^10.8.0
  infinite_scroll_tab_view: ^1.0.3
  country_picker: ^2.0.27
  # stepper_a: ^1.3.1
  # timelines_plus: ^1.0.6
  easy_stepper: ^0.8.5+1
  language_picker: ^0.4.5
  another_stepper: ^1.2.2
  google_sign_in_web: ^0.12.4+4
  enhance_stepper: ^1.0.1
  multi_select_flutter: ^4.1.3
  shared_preferences: ^2.5.2
  image_picker: ^1.1.2
  firebase_storage: ^12.4.4
  fl_chart: ^0.70.2
  google_fonts: ^6.2.1
  url_launcher: ^6.3.1
  # expandable_widgets: ^1.0.3+1
  expandable: ^5.0.1
  csv: ^6.0.0
  universal_html: ^2.2.4
  intl: 
  cloud_functions: ^5.4.0
  shimmer: ^2.0.0
  visibility_detector: ^0.4.0+2
  flutter_animate: ^4.5.2
  flutter_markdown: ^0.7.0
  markdown_editor_plus: ^0.2.13
  fluttertoast: ^8.2.12
  # flutter_quill: 

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Alatsi
      fonts:
        - asset: fonts/Alatsi-Regular.ttf
    - family: Cairo
      fonts:
        - asset: fonts/Cairo-Regular.ttf
    - family: Space
      fonts:
        - asset: fonts/SpaceGrotesk-Regular.ttf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
